#!/bin/bash

# install homebrew
command -v brew >/dev/null 2>&1 || { echo >&2 "Installing Homebrew Now"; \
/usr/bin/ruby -e "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)"; }

# install pyenv
command -v pyenv >/dev/null 2>&1 || { echo >&2 "Installing pyenv"; brew install pyenv; }

# install python version
if command -v pyenv >/dev/null 2>&1; then
  PYTHON_VERSION=$(cat .python-version)
  if ! pyenv versions --bare | grep -q "^$PYTHON_VERSION$"; then
    echo "Installing Python $PYTHON_VERSION"
    pyenv install "$PYTHON_VERSION"
  else
    echo "Python $PYTHON_VERSION is already installed"
  fi
fi


# install setup tools for python
brew install python-setuptools pkg-config cairo libpng jpeg giflib pango
pip install setuptools
# install ollama
# command -v ollama >/dev/null 2>&1 || { echo >&2 "Installing Ollama"; \
# brew install ollama; }


# serve ollama
# if you want to run ollama, use --ollama flag
if [[ "$*" == *"--ollama"* ]]; then
  echo "Starting Ollama server and running mistral-nemo model"
  ollama serve & ollama run mistral-nemo 2>&1 &
else
  echo "Skipping Ollama server (use --ollama flag to start it)"
fi

# install nvm
if [ -d "${HOME}/.nvm/.git" ]; then 
    echo "nvm installed"; 
else 
    echo "nvm not installed, installing nvm"; 
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash
fi
# source nvm to get nvm command
source ~/.nvm/nvm.sh
# install current node version
nvm install
npm install -g pnpm
npm install -g node-gyp

# install current node modules (using npm vs pnpm because of pnpm max packages issues)
npm install --force
npm install canvas

node node_modules/couchbase/scripts/install.js
# fix cpu features not missing buildcheck.gypi (may be fixed, but attempts nonetheless)
node node_modules/cpu-features@0.0.10/node_modules/cpu-features/buildcheck.js > node_modules/cpu-features@0.0.10/node_modules/cpu-features/buildcheck.gypi

#unclear but electron doesn't work off the bat, so wanting to just run this for now.
node node_modules/electron/install.js

export NODE_OPTIONS="--max-old-space-size=16384"

# install and build flowise (using pnpm)
cd ./Flowise
pnpm install && pnpm build
cd ..