# only allow install script to manage package.jsons
package.json

# editor
.idea
.vscode

# dependencies
**/node_modules
**/package-lock.json
**/yarn.lock

## logs
**/logs
**/*.log

## pnpm
.pnpm-store/

## build
**/dist
**/build
**/out

## temp
**/tmp
**/temp

## test
**/coverage

# misc
.DS_Store

## env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

## turbo
.turbo

## secrets
**/*.key
**/api.json

## uploads
**/uploads

## compressed
**/*.tgz

## vscode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

## other keys
*.key
*.keys
*.priv
*.rsa
*.key.json

## ssh keys
*.ssh
*.ssh-key
.key-mrc

## Certificate Authority
*.ca

## Certificate
*.crt

## Certificate Sign Request
*.csr

## Certificate
*.der 

## Key database file
*.kdb

## OSCP request data
*.org

## PKCS #12
*.p12

## PEM-encoded certificate data
*.pem

## Random number seed
*.rnd

## SSLeay data
*.ssleay

## S/MIME message
*.smime
*.vsix
