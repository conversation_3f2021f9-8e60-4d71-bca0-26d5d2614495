{"name": "souls-wise", "version": "3.0.2", "description": "SOULSai Server", "main": "dist/index", "types": "dist/index.d.ts", "bin": {"souls": "./bin/run"}, "files": ["bin", "marketplaces", "dist", "npm-shrinkwrap.json", "oclif.manifest.json", "oauth2.html"], "oclif": {"bin": "souls", "commands": "./dist/commands"}, "scripts": {"build": "tsc && rimraf dist/enterprise/emails && gulp", "start": "run-script-os", "clean": "<PERSON><PERSON><PERSON> dist", "nuke": "rimraf dist node_modules .turbo", "start:windows": "cd bin && run start", "start:default": "cd bin && ./run start", "start-worker:windows": "cd bin && run worker", "start-worker:default": "cd bin && ./run worker", "dev": "nodemon", "oclif-dev": "run-script-os", "oclif-dev:windows": "cd bin && dev start", "oclif-dev:default": "cd bin && ./dev start", "postpack": "shx rm -f oclif.manifest.json", "prepack": "pnpm build && oclif manifest && oclif readme", "typeorm": "typeorm-ts-node-commonjs", "typeorm:migration-generate": "pnpm typeorm migration:generate -d ./src/utils/typeormDataSource.ts", "typeorm:migration-run": "pnpm typeorm migration:run -d ./src/utils/typeormDataSource.ts", "typeorm:migration-revert": "pnpm typeorm migration:revert -d ./src/utils/typeormDataSource.ts", "watch": "tsc --watch", "version": "oclif readme && git add README.md", "cypress:open": "cypress open", "cypress:run": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "cypress:ci": "START_SERVER_AND_TEST_INSECURE=1 start-server-and-test start https-get://localhost:3000 cypress:run", "test": "jest --runInBand --detectOpenHandles --forceExit"}, "keywords": [], "homepage": "https://sou.ls", "author": {"name": "<PERSON>"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.699.0", "@google-cloud/logging-winston": "^6.0.0", "@keyv/redis": "^4.2.0", "@oclif/core": "4.0.7", "@opentelemetry/api": "^1.3.0", "@opentelemetry/auto-instrumentations-node": "^0.52.0", "@opentelemetry/core": "1.27.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.54.0", "@opentelemetry/exporter-metrics-otlp-http": "0.54.0", "@opentelemetry/exporter-metrics-otlp-proto": "0.54.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.54.0", "@opentelemetry/exporter-trace-otlp-http": "0.54.0", "@opentelemetry/exporter-trace-otlp-proto": "0.54.0", "@opentelemetry/resources": "1.27.0", "@opentelemetry/sdk-metrics": "1.27.0", "@opentelemetry/sdk-node": "^0.54.0", "@opentelemetry/sdk-trace-base": "1.27.0", "@opentelemetry/semantic-conventions": "1.27.0", "@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.14.202", "@types/passport": "^1.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/uuid": "^9.0.7", "async-mutex": "^0.4.0", "axios": "1.7.9", "bcryptjs": "^2.4.3", "bull-board": "^2.1.3", "bullmq": "5.45.2", "cache-manager": "^6.3.2", "connect-pg-simple": "^10.0.0", "connect-redis": "^8.0.1", "connect-sqlite3": "^0.9.15", "content-disposition": "0.5.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.1.1", "csv-parser": "^3.0.0", "dotenv": "^16.0.0", "express": "^4.17.3", "express-basic-auth": "^1.2.1", "express-mysql-session": "^3.0.3", "express-rate-limit": "^6.9.0", "express-session": "^1.18.1", "souls-components": "*", "souls-ui": "*", "flowise-nim-container-manager": "^1.0.11", "global-agent": "^3.0.0", "gulp": "^4.0.2", "handlebars": "^4.7.8", "http-errors": "^2.0.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "langchainhub": "^0.0.11", "lodash": "^4.17.21", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "multer": "^1.4.5-lts.1", "multer-cloud-storage": "^4.0.0", "multer-s3": "^3.0.1", "mysql2": "^3.11.3", "nanoid": "3", "nodemailer": "^6.9.14", "openai": "^4.96.0", "passport": "^0.7.0", "passport-auth0": "^1.4.4", "passport-cookie": "^1.0.9", "passport-github": "^1.1.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-openidconnect": "^0.1.2", "pg": "^8.11.1", "posthog-node": "^3.5.0", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "reflect-metadata": "^0.1.13", "s3-streamlogger": "^1.11.0", "sanitize-html": "^2.11.0", "sqlite3": "^5.1.6", "stripe": "^15.6.0", "turndown": "^7.2.0", "typeorm": "^0.3.6", "uuid": "^9.0.1", "winston": "^3.9.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/content-disposition": "0.5.8", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.7", "@types/multer-s3": "^3.0.3", "@types/nodemailer": "^6.4.15", "@types/passport-auth0": "^1.0.9", "@types/passport-github": "^1.1.12", "@types/passport-openidconnect": "^0.1.3", "@types/sanitize-html": "^2.9.5", "@types/supertest": "^6.0.3", "@types/turndown": "^5.0.5", "concurrently": "^7.1.0", "cypress": "^13.13.0", "jest": "^29.7.0", "nodemon": "^2.0.22", "oclif": "^3", "rimraf": "^5.0.5", "run-script-os": "^1.1.6", "shx": "^0.3.3", "start-server-and-test": "^2.0.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.7.0", "tsc-watch": "^6.0.4", "typescript": "^5.4.5"}}