{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "enable": true, "expr": "ALERTS", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "<PERSON><PERSON><PERSON>", "showIn": 0, "step": "10s", "type": "alert"}]}, "description": "Application metrics", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(rate(internal_predictions[1m])) by (status)  * 60", "fullMetaSearch": false, "hide": false, "includeNullMetadata": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Internal Predictions", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 7, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "refId": "A"}], "title": "Throughput", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.1}, {"color": "rgba(245, 54, 54, 0.9)"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 9}, "hideTimeOverride": false, "id": 6, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "/^Value$/", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "sum(increase(http_request_duration_ms_count{code=~\"^5..$\"}[1m])) /  sum(increase(http_request_duration_ms_count[1m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 20}], "title": "Error rate", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "rpm"}, "overrides": []}, "gridPos": {"h": 7, "w": 18, "x": 6, "y": 9}, "id": 1, "links": [{"url": "/"}], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "sum(rate(http_request_duration_ms_count[1m])) by (service, route, method, code)  * 60", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{service}} - {{method}} {{route}} {{code}}", "metric": "", "refId": "A", "step": 2}], "title": "Throughput", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 8, "panels": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 17}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_ms_bucket[1m])) by (le, service, route, method))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{service}} - {{method}} {{route}}", "refId": "A", "step": 2}], "title": "Median Response Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 24}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_ms_bucket[1m])) by (le, service, route, method))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{service}} - {{method}} {{route}}", "refId": "A", "step": 2}], "title": "95th Response Time", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "refId": "A"}], "title": "Response time", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 10, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "cds4j1ybfuhogb"}, "refId": "A"}], "title": "Business", "type": "row"}], "refresh": "10s", "schemaVersion": 39, "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "FlowiseAI - Custom Metrics", "uid": "dds4pojnfec5cc", "version": 8, "weekStart": ""}