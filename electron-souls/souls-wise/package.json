{"name": "soulswise-monorepo", "version": "3.0.3", "private": true, "homepage": "https://flowiseai.com", "workspaces": ["packages/*", "soulswise", "ui", "components", "api-documentation"], "scripts": {"build": "turbo run build", "build-force": "pnpm clean && turbo run build --force", "dev": "turbo run dev --parallel --no-cache", "start": "run-script-os", "start:windows": "cd packages/server/bin && run start", "start:default": "cd packages/server/bin && ./run start", "start-worker": "run-script-os", "start-worker:windows": "cd packages/server/bin && run worker", "start-worker:default": "cd packages/server/bin && ./run worker", "user": "run-script-os", "user:windows": "cd packages/server/bin && run user", "user:default": "cd packages/server/bin && ./run user", "test": "turbo run test", "clean": "pnpm --filter \"./packages/**\" clean", "nuke": "pnpm --filter \"./packages/**\" nuke && rimraf node_modules .turbo", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "eslint \"**/*.{js,jsx,ts,tsx,json,md}\"", "lint-fix": "pnpm lint --fix", "quick": "pretty-quick --staged", "postinstall": "husky install", "migration:create": "pnpm typeorm migration:create"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "eslint --fix"}, "devDependencies": {"@babel/preset-env": "^7.19.4", "@babel/preset-typescript": "7.18.6", "@types/express": "^4.17.13", "@typescript-eslint/typescript-estree": "^7.13.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.1", "kill-port": "^2.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "run-script-os": "^1.1.6", "turbo": "1.10.16", "typescript": "5.4.5"}, "overrides": {"@google/generative-ai": "^0.24.0", "@grpc/grpc-js": "^1.10.10", "@langchain/core": "0.3.37", "@qdrant/openapi-typescript-fetch": "1.2.6", "openai": "4.96.0", "protobufjs": "7.4.0", "mongodb": "6.3.0", "officeparser": "5.1.1", "canvas": "^3.1.2", "@material-ui/core": "5.0.0-beta.0", "@material-ui/icons": "5.0.0-beta.0", "react": "18.2.0", "axios": "1.7.9", "body-parser": "2.0.2", "braces": "3.0.3", "cross-spawn": "7.0.6", "glob-parent": "6.0.2", "http-proxy-middleware": "3.0.3", "json5": "2.2.3", "nth-check": "2.1.1", "path-to-regexp": "0.1.12", "prismjs": "1.29.0", "semver": "7.7.1", "set-value": "4.1.0", "unset-value": "2.0.1", "webpack-dev-middleware": "7.4.2", "@langchain/community": "0.3.29", "@mui/system": "5.15.0", "@mui/material": "5.15.0", "zod": "3.23.8", "@mui/icons-material": "5.15.0", "react-dom": "18.2.0", "typescript": "5.4.5", "@typescript-eslint/eslint-plugin": "6.21.0"}, "pnpm": {"onlyBuiltDependencies": ["faiss-node", "sqlite3"], "overrides": {"axios": "1.7.9", "body-parser": "2.0.2", "braces": "3.0.3", "cross-spawn": "7.0.6", "glob-parent": "6.0.2", "http-proxy-middleware": "3.0.3", "json5": "2.2.3", "nth-check": "2.1.1", "path-to-regexp": "0.1.12", "prismjs": "1.29.0", "semver": "7.7.1", "set-value": "4.1.0", "unset-value": "2.0.1", "webpack-dev-middleware": "7.4.2"}}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20", "pnpm": ">=9"}, "resolutions": {"@google/generative-ai": "^0.24.0", "@grpc/grpc-js": "^1.10.10", "@langchain/core": "0.3.61", "@qdrant/openapi-typescript-fetch": "1.2.6", "openai": "4.96.0", "protobufjs": "7.4.0"}, "eslintIgnore": ["**/dist", "**/node_modules", "**/build", "**/package-lock.json"], "prettier": {"printWidth": 140, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "tabWidth": 4, "semi": false, "endOfLine": "auto"}, "babel": {"presets": ["@babel/preset-typescript", ["@babel/preset-env", {"targets": {"node": "current"}}]]}}